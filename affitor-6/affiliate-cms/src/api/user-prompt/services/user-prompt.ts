/**
 * user-prompt service
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreService('api::user-prompt.user-prompt', ({ strapi }) => ({
  /**
   * Create a new user prompt
   */
  async createUserPrompt(userId: number, promptData: any) {
    try {
      const prompt = await strapi.entityService.create('api::user-prompt.user-prompt', {
        data: {
          ...promptData,
          users_permissions_user: userId,
          publishedAt: new Date(),
        },
      });
      return prompt;
    } catch (error) {
      console.error('Error creating user prompt:', error);
      throw error;
    }
  },

  /**
   * Get user prompts with search and filtering
   */
  async getUserPrompts(userId: number, options: {
    search?: string;
    tags?: string[];
    isFavorite?: boolean;
    limit?: number;
    start?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  } = {}) {
    try {
      const {
        search,
        tags,
        isFavorite,
        limit = 25,
        start = 0,
        sortBy = 'updatedAt',
        sortOrder = 'desc'
      } = options;

      // Build filters
      const filters: any = {
        users_permissions_user: userId,
      };

      if (isFavorite !== undefined) {
        filters.isFavorite = isFavorite;
      }

      if (search) {
        filters.$or = [
          { title: { $containsi: search } },
          { content: { $containsi: search } },
          { description: { $containsi: search } },
        ];
      }

      if (tags && tags.length > 0) {
        // Filter by tags - check if any of the provided tags exist in the prompt's tags array
        filters.tags = { $in: tags };
      }

      const prompts = await strapi.entityService.findMany('api::user-prompt.user-prompt', {
        filters,
        sort: `${sortBy}:${sortOrder}`,
        pagination: {
          start,
          limit,
        },
        populate: ['users_permissions_user'],
      });

      return prompts;
    } catch (error) {
      console.error('Error fetching user prompts:', error);
      throw error;
    }
  },

  /**
   * Get a single user prompt by ID
   */
  async getUserPromptById(userId: number, promptId: string) {
    try {
      const prompt = await strapi.entityService.findOne('api::user-prompt.user-prompt', promptId, {
        filters: {
          users_permissions_user: userId,
        },
        populate: ['users_permissions_user'],
      });
      return prompt;
    } catch (error) {
      console.error('Error fetching user prompt by ID:', error);
      throw error;
    }
  },

  /**
   * Update a user prompt
   */
  async updateUserPrompt(userId: number, promptId: string, updateData: any) {
    try {
      // First verify the prompt belongs to the user
      const existingPrompt = await this.getUserPromptById(userId, promptId);
      if (!existingPrompt) {
        throw new Error('Prompt not found or access denied');
      }

      const updatedPrompt = await strapi.entityService.update('api::user-prompt.user-prompt', promptId, {
        data: updateData,
      });
      return updatedPrompt;
    } catch (error) {
      console.error('Error updating user prompt:', error);
      throw error;
    }
  },

  /**
   * Delete a user prompt
   */
  async deleteUserPrompt(userId: number, promptId: string) {
    try {
      // First verify the prompt belongs to the user
      const existingPrompt = await this.getUserPromptById(userId, promptId);
      if (!existingPrompt) {
        throw new Error('Prompt not found or access denied');
      }

      await strapi.entityService.delete('api::user-prompt.user-prompt', promptId);
      return { success: true };
    } catch (error) {
      console.error('Error deleting user prompt:', error);
      throw error;
    }
  },

  /**
   * Toggle favorite status of a prompt
   */
  async toggleFavorite(userId: number, promptId: string) {
    try {
      const prompt = await this.getUserPromptById(userId, promptId);
      if (!prompt) {
        throw new Error('Prompt not found or access denied');
      }

      const updatedPrompt = await strapi.entityService.update('api::user-prompt.user-prompt', promptId, {
        data: {
          isFavorite: !prompt.isFavorite,
        },
      });
      return updatedPrompt;
    } catch (error) {
      console.error('Error toggling favorite status:', error);
      throw error;
    }
  },

  /**
   * Increment usage count and update last used date
   */
  async incrementUsage(userId: number, promptId: string) {
    try {
      const prompt = await this.getUserPromptById(userId, promptId);
      if (!prompt) {
        throw new Error('Prompt not found or access denied');
      }

      const updatedPrompt = await strapi.entityService.update('api::user-prompt.user-prompt', promptId, {
        data: {
          usageCount: (prompt.usageCount || 0) + 1,
          lastUsedAt: new Date(),
        },
      });
      return updatedPrompt;
    } catch (error) {
      console.error('Error incrementing usage count:', error);
      throw error;
    }
  },

  /**
   * Get recent prompts (last 10 used)
   */
  async getRecentPrompts(userId: number) {
    try {
      const prompts = await strapi.entityService.findMany('api::user-prompt.user-prompt', {
        filters: {
          users_permissions_user: userId,
          lastUsedAt: { $notNull: true },
        },
        sort: 'lastUsedAt:desc',
        pagination: {
          start: 0,
          limit: 10,
        },
      });
      return prompts;
    } catch (error) {
      console.error('Error fetching recent prompts:', error);
      throw error;
    }
  },

  /**
   * Get all unique tags for a user
   */
  async getUserTags(userId: number) {
    try {
      const prompts = await strapi.entityService.findMany('api::user-prompt.user-prompt', {
        filters: {
          users_permissions_user: userId,
        },
        fields: ['tags'],
      });

      // Extract and flatten all tags
      const allTags = prompts.reduce((acc: string[], prompt: any) => {
        if (prompt.tags && Array.isArray(prompt.tags)) {
          acc.push(...prompt.tags);
        }
        return acc;
      }, []);

      // Return unique tags
      return [...new Set(allTags)].sort();
    } catch (error) {
      console.error('Error fetching user tags:', error);
      throw error;
    }
  },
}));
