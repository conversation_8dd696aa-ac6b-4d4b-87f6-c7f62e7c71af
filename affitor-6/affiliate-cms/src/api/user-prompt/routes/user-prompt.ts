/**
 * user-prompt router
 */

export default {
  routes: [
    // Standard CRUD routes
    {
      method: 'POST',
      path: '/user-prompts',
      handler: 'user-prompt.create',
      config: {
        policies: ['plugin::users-permissions.isAuthenticated'],
      },
    },
    {
      method: 'GET',
      path: '/user-prompts',
      handler: 'user-prompt.find',
      config: {
        policies: ['plugin::users-permissions.isAuthenticated'],
      },
    },
    {
      method: 'GET',
      path: '/user-prompts/:id',
      handler: 'user-prompt.findOne',
      config: {
        policies: ['plugin::users-permissions.isAuthenticated'],
      },
    },
    {
      method: 'PUT',
      path: '/user-prompts/:id',
      handler: 'user-prompt.update',
      config: {
        policies: ['plugin::users-permissions.isAuthenticated'],
      },
    },
    {
      method: 'DELETE',
      path: '/user-prompts/:id',
      handler: 'user-prompt.delete',
      config: {
        policies: ['plugin::users-permissions.isAuthenticated'],
      },
    },

    // Custom routes
    {
      method: 'POST',
      path: '/user-prompts/:id/favorite',
      handler: 'user-prompt.toggleFavorite',
      config: {
        policies: ['plugin::users-permissions.isAuthenticated'],
      },
    },
    {
      method: 'POST',
      path: '/user-prompts/:id/use',
      handler: 'user-prompt.incrementUsage',
      config: {
        policies: ['plugin::users-permissions.isAuthenticated'],
      },
    },
    {
      method: 'GET',
      path: '/user-prompts/recent',
      handler: 'user-prompt.getRecent',
      config: {
        policies: ['plugin::users-permissions.isAuthenticated'],
      },
    },
    {
      method: 'GET',
      path: '/user-prompts/tags',
      handler: 'user-prompt.getTags',
      config: {
        policies: ['plugin::users-permissions.isAuthenticated'],
      },
    },
  ],
};
