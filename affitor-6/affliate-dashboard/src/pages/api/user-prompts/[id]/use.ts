import { NextApiRequest, NextApiResponse } from 'next';
import { createApiContext } from '@/utils/api-middleware';
import { sendApiError } from '@/utils/api-error-handler';
import { StrapiClient } from '@/utils/request';

interface UserPrompt {
  id: string;
  title: string;
  content: string;
  description?: string;
  tags?: string[];
  isFavorite: boolean;
  usageCount: number;
  lastUsedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<{ data: UserPrompt; message: string } | any>
) {
  try {
    const { token } = createApiContext(req, { requireAuth: true });
    const { id } = req.query;

    if (!id || typeof id !== 'string') {
      return res.status(400).json({ error: 'Invalid prompt ID' });
    }

    if (req.method === 'POST') {
      // Increment usage count
      const response = await StrapiClient.client.post(`/api/user-prompts/${id}/use`, {}, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      return res.status(200).json(response.data);
    }

    // Method not allowed
    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error: any) {
    console.error('Error in increment usage API route:', error);
    sendApiError(res, error, 'Error incrementing usage count');
  }
}
